import {StyleSheet} from 'react-native';
import {FONTS, IOS, COLORS} from './constant';

const commonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  textInputHeader: {
    fontSize: 14,
    fontFamily: FONTS.POPPINS.REGULAR,
    color: COLORS.BLACK,
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  viewSearchTop: {
    backgroundColor: '#F2F5F9',
    borderColor: '#D7E2F1',
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginLeft: 15,
    marginRight: 15,
  },
  searchInnerContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    marginLeft: 7,
    fontFamily: FONTS.POPPINS.REGULAR,
    fontSize: 17,
    flex: 1,
    textAlignVertical: 'center',
    textAlign: 'left',
  },
  headerTitle: {
    marginLeft: 30,
    fontSize: 17,
    fontFamily: FONTS.POPPINS.MEDIUM,
  },
  textHereInputView: {
    gap: 5,
    flexDirection: 'column',
    alignItems: 'stretch',
    justifyContent: 'space-between',
  },
  textHereInputHeader: {
    fontFamily: FONTS.POPPINS.MEDIUM,
    fontSize: 16,
    height: 21,
    color: 'black',
  },
  textHereView: {
    marginTop: 5,
    height: 150,
    borderColor: COLORS.BORDER_COLOR,
    borderWidth: 1,
    padding: 15,
    borderRadius: 10,
    fontFamily: FONTS.POPPINS.REGULAR,
    fontSize: 16,
  },
  bottomButton: {
    marginTop: 20,
    marginBottom: 20,
    paddingVertical: 6,
    borderRadius: 10,
  },
  bottomButtonLabel: {
    fontSize: 16,
    color: '#fff',
    fontFamily: FONTS.POPPINS.MEDIUM,
  },
  labelTopInstructor: {
    marginLeft: 15,
    marginRight: 15,
    marginTop: 5,
    marginBottom: 5,
    fontFamily: FONTS.POPPINS.REGULAR,
    fontSize: 12,
    color: COLORS.BLACK,
  },
  instructionText: {
    fontFamily: FONTS.POPPINS.MEDIUM,
    fontSize: 16,
    lineHeight: 22,
    color: COLORS.BLACK,
  },
  instructionTextTopSpace: {marginTop: 10},
  instructionTextBottomSpace: {marginBottom: 10},
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#0000FF',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: FONTS.POPPINS.REGULAR,
  },
  noDataText: {
    textAlign: 'center',
    justifyContent: 'center',
    marginTop: 50,
    fontSize: 16,
    color: COLORS.DARK_GRAY,
    fontFamily: FONTS.POPPINS.REGULAR,
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#ffebee',
    marginHorizontal: 12,
    marginVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  errorText: {
    color: '#c62828',
    marginBottom: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: FONTS.POPPINS.REGULAR,
    color: '#666',
    textAlign: 'center',
  },
  fullHeight: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingBottom: IOS ? 120 : 90, // Extra padding at bottom for iOS and Android
  },
  footerLoader: {
    paddingVertical: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  footerText: {
    marginLeft: 8,
    color: '#666',
  },
  clearButton: {
    padding: 8,
    marginRight: 5,
  },
  clearButtonText: {
    color: '#888',
    fontSize: 16,
    fontWeight: '600',
  },
  nativeAdContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingHorizontal: 15,
    paddingVertical: 10,
    paddingBottom: 15, // Add bottom padding since we removed bottom: 15
  },
});

export default commonStyles;
