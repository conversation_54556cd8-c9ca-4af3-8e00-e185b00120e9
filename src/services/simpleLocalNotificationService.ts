import {Platform, PermissionsAndroid, Linking, Alert} from 'react-native';
import PushNotification from 'react-native-push-notification';

/**
 * Simple Local Notification Service
 * This service avoids Firebase initialization by using only local notification features
 */
class SimpleLocalNotificationService {
  private isInitialized = false;
  private channelCreated = false;

  /**
   * Initialize simple local notifications (no Firebase)
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('[SimpleLocalNotification] Already initialized');
      return;
    }

    if (Platform.OS !== 'android') {
      console.log('[SimpleLocalNotification] iOS not supported in this app');
      return;
    }

    try {
      console.log(
        '[SimpleLocalNotification] Initializing simple local notifications...',
      );

      // Request permissions first
      const hasPermissions = await this.requestAndroidPermissions();
      if (!hasPermissions) {
        console.warn(
          '[SimpleLocalNotification] Permissions not granted, but continuing',
        );
      }

      // Create notification channel directly without full PushNotification.configure
      await this.createNotificationChannel();

      this.isInitialized = true;
      console.log(
        '[SimpleLocalNotification] Initialized successfully (no Firebase)',
      );
    } catch (error) {
      console.error('[SimpleLocalNotification] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Create notification channel for Android
   */
  private async createNotificationChannel(): Promise<void> {
    if (this.channelCreated) {
      return;
    }

    try {
      console.log('[SimpleLocalNotification] Creating notification channel...');

      PushNotification.createChannel(
        {
          channelId: 'simple-local-notifications',
          channelName: 'Simple Local Notifications',
          channelDescription: 'Local notifications without Firebase',
          playSound: true,
          soundName: 'default',
          importance: 4,
          vibrate: true,
        },
        created => {
          console.log(`[SimpleLocalNotification] Channel created: ${created}`);
          this.channelCreated = true;
        },
      );
    } catch (error) {
      console.error(
        '[SimpleLocalNotification] Failed to create channel:',
        error,
      );
    }
  }

  /**
   * Request Android notification permissions
   */
  async requestAndroidPermissions(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return true;
    }

    try {
      console.log(
        '[SimpleLocalNotification] Requesting Android permissions...',
      );
      console.log(
        '[SimpleLocalNotification] Platform.Version:',
        Platform.Version,
      );

      if (Platform.Version >= 33) {
        console.log(
          '[SimpleLocalNotification] Android 13+ detected, checking POST_NOTIFICATIONS permission...',
        );

        // Check if permission is already granted
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        console.log(
          '[SimpleLocalNotification] Current permission status:',
          hasPermission,
        );

        if (hasPermission) {
          console.log(
            '[SimpleLocalNotification] POST_NOTIFICATIONS permission already granted',
          );
          return true;
        }

        console.log(
          '[SimpleLocalNotification] Requesting POST_NOTIFICATIONS permission...',
        );
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          {
            title: 'Enable Notifications',
            message:
              'This app needs notification permission to send you reminders. Please tap "Allow" to continue.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: "Don't Allow",
            buttonPositive: 'Allow',
          },
        );

        console.log(
          '[SimpleLocalNotification] Permission request result:',
          granted,
        );
        const isGranted = granted === PermissionsAndroid.RESULTS.GRANTED;
        console.log('[SimpleLocalNotification] Permission granted:', isGranted);

        if (!isGranted) {
          console.warn(
            '[SimpleLocalNotification] Permission denied. Please enable notifications manually in Settings > Apps > [App Name] > Notifications',
          );
        }

        return isGranted;
      }

      console.log(
        '[SimpleLocalNotification] Android < 33, permissions granted at install',
      );
      return true;
    } catch (error) {
      console.error(
        '[SimpleLocalNotification] Permission request failed:',
        error,
      );
      return false;
    }
  }

  /**
   * Send immediate local notification
   */
  sendImmediateNotification(title: string, message: string): void {
    if (!this.isInitialized) {
      console.warn('[SimpleLocalNotification] Service not initialized');
      return;
    }

    try {
      console.log(
        '[SimpleLocalNotification] Sending immediate notification...',
      );

      PushNotification.localNotification({
        channelId: 'simple-local-notifications',
        title: title,
        message: message,
        playSound: true,
        soundName: 'default',
        importance: 'high',
        priority: 'high',
        vibrate: true,
        vibration: 300,
        autoCancel: true,
      });

      console.log('[SimpleLocalNotification] Immediate notification sent');
    } catch (error) {
      console.error(
        '[SimpleLocalNotification] Failed to send notification:',
        error,
      );
    }
  }

  /**
   * Schedule local notification
   */
  scheduleNotification(
    title: string,
    message: string,
    date: Date,
    id?: string,
  ): string {
    if (!this.isInitialized) {
      throw new Error('Simple local notification service not initialized');
    }

    const notificationId = id || `simple_${Date.now()}`;

    try {
      console.log(
        '[SimpleLocalNotification] Scheduling notification for:',
        date.toISOString(),
      );

      PushNotification.localNotificationSchedule({
        channelId: 'simple-local-notifications',
        title: title,
        message: message,
        date: date,
        playSound: true,
        soundName: 'default',
        importance: 'high',
        priority: 'high',
        vibrate: true,
        vibration: 300,
        autoCancel: true,
        allowWhileIdle: true,
      });

      console.log(
        `[SimpleLocalNotification] Scheduled notification ${notificationId}`,
      );
      return notificationId;
    } catch (error) {
      console.error(
        '[SimpleLocalNotification] Failed to schedule notification:',
        error,
      );
      throw error;
    }
  }

  /**
   * Cancel notification
   */
  cancelNotification(notificationId: string): void {
    try {
      console.log(
        `[SimpleLocalNotification] Cancelling notification: ${notificationId}`,
      );
      PushNotification.cancelLocalNotification(notificationId);
    } catch (error) {
      console.error(
        '[SimpleLocalNotification] Failed to cancel notification:',
        error,
      );
    }
  }

  /**
   * Test simple notification
   */
  testSimpleNotification(): void {
    this.sendImmediateNotification(
      'Simple Test',
      'This is a simple local notification (no Firebase)!',
    );
  }

  /**
   * Test scheduled notification (5 seconds)
   */
  testScheduledNotification(): void {
    const testDate = new Date(Date.now() + 5000);
    this.scheduleNotification(
      'Scheduled Test',
      'This notification was scheduled 5 seconds ago!',
      testDate,
      'test_scheduled_simple',
    );
  }

  /**
   * Open app notification settings manually
   */
  openNotificationSettings(): void {
    Alert.alert(
      'Enable Notifications',
      'To receive notifications, please:\n\n1. Tap "Open Settings" below\n2. Find "Notifications" or "App notifications"\n3. Enable notifications for this app\n4. Come back and test again',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Open Settings',
          onPress: () => {
            Linking.openSettings().catch(err => {
              console.error('Failed to open settings:', err);
              Alert.alert(
                'Error',
                'Could not open settings. Please go to Settings > Apps > [App Name] > Notifications manually.',
              );
            });
          },
        },
      ],
    );
  }

  /**
   * Check and guide user through permission setup
   */
  async checkAndGuidePermissions(): Promise<boolean> {
    const hasPermissions = await this.requestAndroidPermissions();

    if (!hasPermissions) {
      Alert.alert(
        'Notifications Disabled',
        'Notifications are currently disabled. Would you like to enable them in Settings?',
        [
          {text: 'Not Now', style: 'cancel'},
          {
            text: 'Open Settings',
            onPress: () => this.openNotificationSettings(),
          },
        ],
      );
    }

    return hasPermissions;
  }
}

const simpleLocalNotificationService = new SimpleLocalNotificationService();
export default simpleLocalNotificationService;
