import React, {useState, useCallback, useEffect, useRef} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  Linking,
  Keyboard,
} from 'react-native';
import CustomSearchBar from '../../components/CustomSearchBar';
import historyRepository, {
  HistoryData,
} from '../../database/watermelon/repositories/historyRepository';
import {useFocusEffect} from '@react-navigation/native';
import {NativeAd} from 'react-native-google-mobile-ads';

import CustomNativeAdCard from '../../components/ads/CustomNativeAdCard';
import adMobService from '../../services/adMobService';
import commonStyles from '../../common/commonStyles';

const HistoryListScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [originalHistoryItems, setOriginalHistoryItems] = useState<
    HistoryData[]
  >([]);
  const [filteredItems, setFilteredItems] = useState<HistoryData[]>([]);

  // Native ad state
  const [nativeAd, setNativeAd] = useState<NativeAd | null>(null);
  const [nativeAdHeight, setNativeAdHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  // Load history data
  const loadHistoryData = useCallback(async () => {
    try {
      setLoading(true);
      const history = await historyRepository.getNumberTapHistory();
      console.log('Number tap history items count:', history.length);

      // Store the original list for search filtering
      setOriginalHistoryItems(history);

      // Apply search filter if query exists
      if (searchQuery && searchQuery.trim() !== '') {
        const trimmedQuery = searchQuery.trim().toLowerCase();
        const filtered = history.filter(item => {
          return (
            item.company_name?.toLowerCase().trim().includes(trimmedQuery) ||
            item.number?.toLowerCase().trim().includes(trimmedQuery)
          );
        });
        setFilteredItems(filtered);
      } else {
        setFilteredItems(history);
      }
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setLoading(false);
    }
  }, [searchQuery]);

  // Reset native ad height when ad is removed
  useEffect(() => {
    if (!nativeAd) {
      setNativeAdHeight(0);
    }
  }, [nativeAd]);

  // Use useEffect for initial load and useFocusEffect for drawer navigation
  useEffect(() => {
    loadHistoryData();
  }, []);

  // Enhanced native ad loading with retry logic - separate from focus effect
  useEffect(() => {
    const loadNativeAd = async () => {
      let attempts = 0;
      const maxAttempts = 3;

      const tryLoadAd = async () => {
        try {
          attempts++;
          console.log(
            `[HistoryScreen] Attempting to load native ad... (attempt ${attempts})`,
          );
          const ad = await adMobService.initiateNativeAd();
          if (ad) {
            console.log('[HistoryScreen] Native ad loaded successfully');
            setNativeAd(ad);
            return true;
          } else {
            console.log('[HistoryScreen] No native ad returned');
            return false;
          }
        } catch (error) {
          console.error('[HistoryScreen] Failed to load native ad:', error);
          return false;
        }
      };

      // Try to load ad
      const success = await tryLoadAd();

      // If failed and we haven't reached max attempts, retry
      if (!success && attempts < maxAttempts) {
        console.log('[HistoryScreen] Retrying in 3 seconds...');
        setTimeout(async () => {
          const retrySuccess = await tryLoadAd();

          // One more retry if still failed
          if (!retrySuccess && attempts < maxAttempts) {
            console.log('[HistoryScreen] Final retry in 5 seconds...');
            setTimeout(async () => {
              await tryLoadAd();
            }, 5000);
          }
        }, 3000);
      }
    };

    loadNativeAd();
  }, []);

  // Use useFocusEffect for drawer navigation - reload data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadHistoryData();

      // Try to load native ad again if not already loaded (for drawer navigation)
      if (!nativeAd) {
        adMobService
          .initiateNativeAd()
          .then(ad => {
            if (ad) {
              setNativeAd(ad);
            }
          })
          .catch(error => {
            console.error(
              '[HistoryScreen] Failed to load native ad on focus:',
              error,
            );
          });
      }

      return () => {
        // Cleanup on unfocus
      };
    }, [nativeAd]),
  );

  // Effect to handle search query changes and ensure data consistency
  useEffect(() => {
    // If search query is empty and we have original data but no filtered data, restore it
    if (
      (!searchQuery || searchQuery.trim() === '') &&
      originalHistoryItemsRef.current.length > 0 &&
      filteredItems.length === 0
    ) {
      console.log(
        'Restoring filtered items from original data due to empty search',
      );
      // Use setTimeout to prevent interference with TextInput
      setTimeout(() => {
        setFilteredItems([...originalHistoryItemsRef.current]);
      }, 0);
    }
  }, [searchQuery, filteredItems.length]); // Keep using searchQuery for filtering logic

  // Keyboard event listeners and cleanup
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      },
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
      // Clear any pending filter timeout
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
    };
  }, []);

  // Filter search data function with optimization
  const filterSearchedData = useCallback(
    (searchText: string) => {
      const currentOriginalItems = originalHistoryItemsRef.current;
      const currentLoadHistoryData = loadHistoryDataRef.current;

      console.log('Filtering history with search text:', searchText);
      console.log('Original history items count:', currentOriginalItems.length);

      if (!searchText || searchText.trim() === '') {
        // Restore the original list if search text is empty
        console.log('Restoring original history list');

        // If originalHistoryItems is empty, reload the data
        if (currentOriginalItems.length === 0) {
          console.log('Original history items is empty, reloading data...');
          currentLoadHistoryData();
          return;
        }

        // Use double requestAnimationFrame to ensure TextInput updates complete first
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            setFilteredItems([...currentOriginalItems]);
          });
        });
        return;
      }

      // Filter history items based on the search text (case-insensitive)
      const trimmedSearchText = searchText.trim().toLowerCase();
      const filteredHistory = currentOriginalItems.filter(item => {
        return (
          item.company_name?.toLowerCase().trim().includes(trimmedSearchText) ||
          item.number?.toLowerCase().trim().includes(trimmedSearchText)
        );
      });

      // Use double requestAnimationFrame to ensure TextInput updates complete first
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setFilteredItems(filteredHistory);
        });
      });
    },
    [], // No dependencies - use refs for current values
  );

  const handlePhoneCall = useCallback(async (number: string) => {
    const phoneNumber = `tel:${number}`;
    Linking.openURL(phoneNumber).catch((err: Error) =>
      console.error('Failed to open phone call:', err),
    );
  }, []);

  // Memoized render item to prevent unnecessary re-renders
  const renderHistoryItem = useCallback(
    ({item}: {item: HistoryData}) => {
      return (
        <View style={styles.numberTapCard}>
          <Text style={styles.companyNameText}>{item.company_name}</Text>
          <TouchableOpacity onPress={() => handlePhoneCall(item.number ?? '')}>
            <Text style={styles.numberText}>{item.number}</Text>
          </TouchableOpacity>
          <Text style={styles.timestampText}>
            {item.viewed_at ? new Date(item.viewed_at).toLocaleString() : ''}
          </Text>
        </View>
      );
    },
    [handlePhoneCall],
  );

  // Create refs for stable access to current values
  const originalHistoryItemsRef = useRef(originalHistoryItems);
  const loadHistoryDataRef = useRef(loadHistoryData);
  const searchQueryRef = useRef(searchQuery);
  const filterTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update refs when values change
  useEffect(() => {
    originalHistoryItemsRef.current = originalHistoryItems;
    loadHistoryDataRef.current = loadHistoryData;
    searchQueryRef.current = searchQuery;
  });

  // Handle search change with complete isolation from filtering
  const handleSearchChange = useCallback(
    (text: string) => {
      console.log('Search text received:', text);

      // Update the query state (CustomSearchBar is debounced)
      setSearchQuery(text);

      // Clear any existing timeout
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }

      // Update search query only if it's different to prevent unnecessary re-renders
      if (searchQueryRef.current !== text) {
        // Use setTimeout to completely decouple state updates
        setTimeout(() => {
          setSearchQuery(text);
        }, 0);
      }

      // If clearing search and we have no original data, reload immediately
      if (
        (!text || text.trim() === '') &&
        originalHistoryItemsRef.current.length === 0
      ) {
        console.log('Search cleared but no original data, reloading...');
        setTimeout(() => {
          loadHistoryDataRef.current();
        }, 0);
        return;
      }

      // Use our own timeout-based debouncing with longer delay
      filterTimeoutRef.current = setTimeout(() => {
        filterSearchedData(text);
      }, 300); // 300ms delay to ensure TextInput is completely stable
    },
    [], // Empty dependency array for maximum stability
  );

  return (
    <SafeAreaView style={styles.container}>
      <CustomSearchBar
        onSearch={handleSearchChange}
        onVoiceResult={handleSearchChange} // Use the same optimized handler
        initialValue={searchQuery}
        placeholder="Search history"
        showVoiceSearch={true}
        debounceTime={250}
      />

      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loaderText}>Loading history...</Text>
        </View>
      ) : filteredItems.length === 0 && originalHistoryItems.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No history found</Text>
        </View>
      ) : filteredItems.length === 0 && originalHistoryItems.length > 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No matching results found</Text>
        </View>
      ) : (
        <FlatList
          style={{padding: 15}}
          contentContainerStyle={{
            paddingBottom:
              nativeAd && !isKeyboardVisible
                ? nativeAdHeight > 0
                  ? nativeAdHeight + 20
                  : 230
                : 55, // Dynamic padding with fallback, no padding when keyboard is visible
          }}
          data={filteredItems}
          extraData={filteredItems.length} // Only re-render when data length changes
          keyExtractor={item => item.id?.toString() || ''}
          renderItem={renderHistoryItem}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          // Performance optimizations to prevent interference with TextInput
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          windowSize={10}
          initialNumToRender={10}
          updateCellsBatchingPeriod={50}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
        />
      )}
      {/* Native Ad - Sticky at bottom, hidden when keyboard is visible */}
      {nativeAd && !isKeyboardVisible && (
        <View
          style={commonStyles.nativeAdContainer}
          onLayout={event => {
            const {height} = event.nativeEvent.layout;
            setNativeAdHeight(height);
          }}>
          <CustomNativeAdCard ad={nativeAd} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    fontFamily: 'Poppins-Regular',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    fontFamily: 'Poppins-Medium',
    textAlign: 'center',
  },
  numberTapCard: {
    flexDirection: 'column',
    alignItems: 'stretch',
    backgroundColor: '#f1f5f9',
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    padding: 15,
    borderColor: '#D7E2F1',
    marginHorizontal: 5,
    justifyContent: 'space-evenly',
  },
  companyNameText: {
    fontSize: 16,
    fontFamily: 'Poppins-Bold',
    color: '#333',
    marginBottom: 5,
  },
  numberText: {
    fontSize: 18,
    fontFamily: 'Poppins-Medium',
    color: '#0066cc',
    textDecorationLine: 'underline',
    marginBottom: 5,
  },
  timestampText: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#666',
  },
});

export default HistoryListScreen;
